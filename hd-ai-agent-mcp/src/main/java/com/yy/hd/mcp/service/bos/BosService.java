package com.yy.hd.mcp.service.bos;

import com.baidubce.auth.DefaultBceSessionCredentials;
import com.baidubce.services.bos.BosClient;
import com.baidubce.services.bos.BosClientConfiguration;
import com.yy.hd.commons.uri.HttpUris;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@Slf4j
@AllArgsConstructor
@Service
public class BosService  {

    private final WebClient defaultWebClient;

    private static final String BOS_URL = HttpUris.BOS_UPLOAD_TOKEN_URI;

    private Mono<GetUploadTokenResp> getTokenFromBos(String fileName) {
        GetUploadTokenReq req = new GetUploadTokenReq();
        req.setBusiId("hd-mcp-server");
        req.setUserId(0L);
        req.setFileName(fileName);
        req.setScene(1);
        return defaultWebClient.post()
                .uri(BOS_URL)
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(req)
                .retrieve()
                .bodyToMono(BosResponse.class)
                .doOnNext(response -> log.info("getTokenFromBos rsp:{}", response))
                .onErrorResume(e -> {
                    log.error("getTokenFromBos fail", e);
                    return Mono.empty();
                }).filter(response -> response.getCode() == 0)
                .map(BosResponse::getData);
    }

    public Mono<String> uploadMinFileData(byte[] binaryData, String fileName, String bucketName) {
        return Mono.defer(() -> getTokenFromBos(fileName))
                .publishOn(Schedulers.boundedElastic())
                .map(tokenResp -> {
                    BosClient bosClient = buildTemporaryBosClient(tokenResp.getEndPoint(), tokenResp.getAk(), tokenResp.getSk(), tokenResp.getToken());
                    bosClient.putObject(bucketName, tokenResp.getFileName(), binaryData);
                    bosClient.shutdown();
                    return tokenResp.getBs2Url() + tokenResp.getFileName();
                });

    }

    private BosClient buildTemporaryBosClient(String endPoint, String ak, String sk, String token) {
        return new BosClient(new BosClientConfiguration()
                .withEndpoint(endPoint)
                .withCredentials(new DefaultBceSessionCredentials(ak, sk, token)));
    }

}
