package com.yy.hd.mcp.service.logs;


import com.yy.hd.commons.uri.HttpUris;
import com.yy.hd.commons.utils.JsonUtils;
import com.yy.hd.mcp.config.AliCloudLogSchemaConfig;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.utils.URIBuilder;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@AllArgsConstructor
@Slf4j
@Service
public class AliCloudLogService {

    private final WebClient defaultWebClient;

    private final AliCloudLogSchemaConfig aliCloudLogSchemaConfig;

    private static final String QUERY_LOG_URL = HttpUris.ALI_CLOUD_LOG_QUERY_URI;

    private static final int MAX_PAGE = 50;

    private static final int LINE = 100;

    private static final Duration TIMEOUT = Duration.ofSeconds(20);

    public Mono<Boolean> checkServer(String server) {
        return Mono.just(aliCloudLogSchemaConfig.getLogs().containsKey(server));
    }

    public Flux<String> getServerList() {
        return Flux.fromIterable(aliCloudLogSchemaConfig.getLogs().keySet());
    }

    public Mono<AliCloudLogParseResult> getAliCloudLogs(String server, String query, String fromTime, String toTime) {
        return Mono.defer(() -> {
            if (StringUtils.isBlank(query)) {
                return Mono.error(new IllegalArgumentException("请输入云日志搜索关键字"));
            }
            AliCloudLogSchemaConfig.AliCloudLogSchema aliCloudLogSchema = aliCloudLogSchemaConfig.getLogs().get(server);
            if (aliCloudLogSchema == null) {
                return Mono.error(new IllegalArgumentException("找不到云日志对应的服务"));
            }

            String project = aliCloudLogSchema.getProject();
            String logStore = aliCloudLogSchema.getLogStore();
            String finalFromTime = StringUtils.isBlank(fromTime) ?
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : fromTime;
            String finalToTime = StringUtils.isBlank(toTime) ?
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) : toTime;

            long fromTimestamp = LocalDateTime.parse(finalFromTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                .toEpochSecond(ZoneOffset.ofHours(8));
            long toTimestamp = LocalDateTime.parse(finalToTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                .toEpochSecond(ZoneOffset.ofHours(8));

            AtomicReference<List<String>> terms = new AtomicReference<>(new ArrayList<>());

            return Flux.range(1, MAX_PAGE)
                .concatMap(page -> {
                    int currentOffset = (page - 1) * LINE;
                    return fetchLogPage(project, logStore, fromTimestamp, toTimestamp, query, currentOffset)
                        .doOnNext(response -> {
                            if (response.getData() != null && !CollectionUtils.isEmpty(response.getData().getLogs())) {
                                List<String> keywords = response.getData()
                                    .getMTerms()
                                    .stream()
                                    .flatMap(List::stream)
                                    .filter(StringUtils::isNotBlank)
                                    .toList();
                                if (CollectionUtils.isEmpty(terms.get())) {
                                    terms.set(new ArrayList<>(keywords));
                                }
                            }
                        });
                })
                .takeUntil(response -> response.getData() == null || CollectionUtils.isEmpty(response.getData().getLogs()))
                .filter(response -> response.getData() != null && !CollectionUtils.isEmpty(response.getData().getLogs()))
                .flatMapIterable(response -> response.getData().getLogs())
                .map(this::convertToAliCloudLog)
                .collectList()
                .map(logs -> {
                    log.info("queryAliCloudLog logs size:{}", logs.size());
                    return new AliCloudLogParseResult(logs, terms.get());
                })
                .onErrorMap(Exception.class, e -> new RuntimeException("查询阿里云日志失败", e));
        });
    }

    private Mono<AliCloudLogQueryResponse> fetchLogPage(String project, String logStore, long fromTimestamp,
                                                       long toTimestamp, String query, int offset) {
        return Mono.fromCallable(() -> {
            try {
                URIBuilder builder = new URIBuilder(QUERY_LOG_URL);
                builder.addParameter("project", project);
                builder.addParameter("logStore", logStore);
                builder.addParameter("from", String.valueOf(fromTimestamp));
                builder.addParameter("to", String.valueOf(toTimestamp));
                builder.addParameter("line", String.valueOf(LINE));
                builder.addParameter("offset", String.valueOf(offset));
                builder.addParameter("query", query);
                return builder.build().toString();
            } catch (Exception e) {
                throw new RuntimeException("构建查询URL失败", e);
            }
        })
        .flatMap(url ->
            defaultWebClient.get()
                .uri(url)
                .retrieve()
                .bodyToMono(String.class)
                .timeout(TIMEOUT)
                .doOnNext(content -> log.info("getAliCloudLogs done, url:{}", url))
                .map(content -> JsonUtils.fromJson(content, AliCloudLogQueryResponse.class))
                .doOnError(error -> log.warn("getAliCloudLogs error, url:{}, error:{}", url, error.getMessage()))
        );
    }

    private AliCloudLog convertToAliCloudLog(AliCloudLogQueryResponse.LogItem logItem) {
        Map<String, String> contentsMap = logItem.getMLogItem().getMContents().stream()
            .collect(Collectors.toMap(
                AliCloudLogQueryResponse.LogContentItem::getMKey,
                AliCloudLogQueryResponse.LogContentItem::getMValue
            ));
        return new AliCloudLog(
            contentsMap.get(AliCloudLogQueryResponse.MKeyColumn.LEVEL.getColumn()),
            contentsMap.get(AliCloudLogQueryResponse.MKeyColumn.TIME.getColumn()),
            contentsMap.get(AliCloudLogQueryResponse.MKeyColumn.TRACE.getColumn()),
            contentsMap.get(AliCloudLogQueryResponse.MKeyColumn.MESSAGE.getColumn())
        );
    }
}
